/* eslint-disable */
import * as types from "./graphql";
import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
  "\n  mutation CompanyUserCreate($input: CompanyUserCreateInput!) {\n    companyUserCreate(input: $input) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n": typeof types.CompanyUserCreateDocument;
  "\n  mutation CompanyUserInvite($companyUserId: ID!) {\n    companyUserInvite(companyUserId: $companyUserId) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n": typeof types.CompanyUserInviteDocument;
  "\n  mutation CompanyUserDeactivate($companyUserId: ID!) {\n    companyUserDeactivate(companyUserId: $companyUserId) {\n      success\n      message\n    }\n  }\n": typeof types.CompanyUserDeactivateDocument;
  "\n  mutation CompanyUserActivate {\n    companyUserActivate {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n": typeof types.CompanyUserActivateDocument;
  "\n  mutation CreateCompanyBasicDetails($input: CompanyCreateBasicDetailsInput!) {\n    companyCreateBasicDetails(input: $input) {\n      id\n      basicDetails {\n        name\n        address\n        phoneNumber\n        email\n        website\n        industry\n        size\n      }\n    }\n  }\n": typeof types.CreateCompanyBasicDetailsDocument;
  "\nmutation CustomerUpsertBasicDetails($input: CustomerUpsertBasicDetailsInput!) {\n    customerUpsertBasicDetails(input: $input) {\n        id\n    }\n  }\n": typeof types.CustomerUpsertBasicDetailsDocument;
  "\n  mutation CustomerUpsertAdditionalDetails($input: CustomerUpsertAdditionalDetailsInput!) {\n      customerUpsertAdditionalDetails(input: $input) {\n          id\n    }\n  }\n": typeof types.CustomerUpsertAdditionalDetailsDocument;
  "\n  mutation MasterProductUpsertDetails($input: MasterProductUpsertInput!) {\n    masterProductUpsertDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n": typeof types.MasterProductUpsertDetailsDocument;
  "\n  mutation MasterProductUpsertAdditionalDetails($input: MasterProductUpsertAdditionalDetailsInput!) {\n    masterProductUpsertAdditionalDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n": typeof types.MasterProductUpsertAdditionalDetailsDocument;
  "\n  query Ok {\n    ok\n  }\n": typeof types.OkDocument;
  "\n  query GetCompany {\n    getCompany {\n      id\n      name\n      status\n      onboarding {\n        id\n        pendingSteps {\n          stepType\n          mandatory\n          completed\n        }\n        completedSteps {\n          stepType\n          mandatory\n          completed\n        }\n      }\n    }\n  }\n": typeof types.GetCompanyDocument;
  "\n  query GetCompanyBasicDetails {\n    getCompany {\n      id\n      basicDetails {\n        name\n        address\n        email\n        website\n        phoneNumber\n        industry\n        size\n      }\n    }\n  }\n": typeof types.GetCompanyBasicDetailsDocument;
  "\n  query GetCompanyUsers {\n    getCompany {\n      id\n      users {\n        id\n        name\n        email\n        status\n        roles\n      }\n    }\n  }\n": typeof types.GetCompanyUsersDocument;
  "\n  query GetCompanyUser {\n    getCompanyUser {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n": typeof types.GetCompanyUserDocument;
  "\n  query GetAllCustomers($status: CustomerStatus, $stage: CustomerStage, $customerType: CustomerType) {\n      getCustomers(\n        filters: {\n          status: $status,\n          stage: $stage,\n          customerType: $customerType\n        }\n      ) {\n        id\n        company {\n          id\n        }\n        status\n        stage\n        type\n        ...on CustomerIndividual {\n          basicDetails {\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n              referralSource\n        }\n      }\n      ... on CustomerBusiness {\n          basicDetails {\n              legalName\n              website\n              size\n              industry\n              referralSource\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n          }\n      }\n    }\n  }\n": typeof types.GetAllCustomersDocument;
  "\n  query GetCustomerById($id: ID!) {\n    getCustomer(id: $id) {\n      id\n      status\n      stage\n      type\n      company {\n        id\n      }\n      ... on CustomerIndividual {\n        basicDetails {\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n          referralSource\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n      ... on CustomerBusiness { \n        basicDetails {\n          legalName\n          website\n          size\n          industry\n          referralSource\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n   }\n}\n": typeof types.GetCustomerByIdDocument;
  "\n    query GetAllProducts($status: ProductStatus) {\n        masterProductsGet(\n            filters: {\n                status: $status\n            }\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n": typeof types.GetAllProductsDocument;
  "\n    query GetProductById($id: ID!) {\n        masterProductGetById(\n            id: $id\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            documents {\n                id\n                file {\n                    signedReadURL\n                    signedWriteURL\n                }\n            }\n            customTags {\n                id\n                key\n                label\n                value\n                type\n                description\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n": typeof types.GetProductByIdDocument;
};
const documents: Documents = {
  "\n  mutation CompanyUserCreate($input: CompanyUserCreateInput!) {\n    companyUserCreate(input: $input) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n":
    types.CompanyUserCreateDocument,
  "\n  mutation CompanyUserInvite($companyUserId: ID!) {\n    companyUserInvite(companyUserId: $companyUserId) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n":
    types.CompanyUserInviteDocument,
  "\n  mutation CompanyUserDeactivate($companyUserId: ID!) {\n    companyUserDeactivate(companyUserId: $companyUserId) {\n      success\n      message\n    }\n  }\n":
    types.CompanyUserDeactivateDocument,
  "\n  mutation CompanyUserActivate {\n    companyUserActivate {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n":
    types.CompanyUserActivateDocument,
  "\n  mutation CreateCompanyBasicDetails($input: CompanyCreateBasicDetailsInput!) {\n    companyCreateBasicDetails(input: $input) {\n      id\n      basicDetails {\n        name\n        address\n        phoneNumber\n        email\n        website\n        industry\n        size\n      }\n    }\n  }\n":
    types.CreateCompanyBasicDetailsDocument,
  "\nmutation CustomerUpsertBasicDetails($input: CustomerUpsertBasicDetailsInput!) {\n    customerUpsertBasicDetails(input: $input) {\n        id\n    }\n  }\n":
    types.CustomerUpsertBasicDetailsDocument,
  "\n  mutation CustomerUpsertAdditionalDetails($input: CustomerUpsertAdditionalDetailsInput!) {\n      customerUpsertAdditionalDetails(input: $input) {\n          id\n    }\n  }\n":
    types.CustomerUpsertAdditionalDetailsDocument,
  "\n  mutation MasterProductUpsertDetails($input: MasterProductUpsertInput!) {\n    masterProductUpsertDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n":
    types.MasterProductUpsertDetailsDocument,
  "\n  mutation MasterProductUpsertAdditionalDetails($input: MasterProductUpsertAdditionalDetailsInput!) {\n    masterProductUpsertAdditionalDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n":
    types.MasterProductUpsertAdditionalDetailsDocument,
  "\n  query Ok {\n    ok\n  }\n": types.OkDocument,
  "\n  query GetCompany {\n    getCompany {\n      id\n      name\n      status\n      onboarding {\n        id\n        pendingSteps {\n          stepType\n          mandatory\n          completed\n        }\n        completedSteps {\n          stepType\n          mandatory\n          completed\n        }\n      }\n    }\n  }\n":
    types.GetCompanyDocument,
  "\n  query GetCompanyBasicDetails {\n    getCompany {\n      id\n      basicDetails {\n        name\n        address\n        email\n        website\n        phoneNumber\n        industry\n        size\n      }\n    }\n  }\n":
    types.GetCompanyBasicDetailsDocument,
  "\n  query GetCompanyUsers {\n    getCompany {\n      id\n      users {\n        id\n        name\n        email\n        status\n        roles\n      }\n    }\n  }\n":
    types.GetCompanyUsersDocument,
  "\n  query GetCompanyUser {\n    getCompanyUser {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n":
    types.GetCompanyUserDocument,
  "\n  query GetAllCustomers($status: CustomerStatus, $stage: CustomerStage, $customerType: CustomerType) {\n      getCustomers(\n        filters: {\n          status: $status,\n          stage: $stage,\n          customerType: $customerType\n        }\n      ) {\n        id\n        company {\n          id\n        }\n        status\n        stage\n        type\n        ...on CustomerIndividual {\n          basicDetails {\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n              referralSource\n        }\n      }\n      ... on CustomerBusiness {\n          basicDetails {\n              legalName\n              website\n              size\n              industry\n              referralSource\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n          }\n      }\n    }\n  }\n":
    types.GetAllCustomersDocument,
  "\n  query GetCustomerById($id: ID!) {\n    getCustomer(id: $id) {\n      id\n      status\n      stage\n      type\n      company {\n        id\n      }\n      ... on CustomerIndividual {\n        basicDetails {\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n          referralSource\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n      ... on CustomerBusiness { \n        basicDetails {\n          legalName\n          website\n          size\n          industry\n          referralSource\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n   }\n}\n":
    types.GetCustomerByIdDocument,
  "\n    query GetAllProducts($status: ProductStatus) {\n        masterProductsGet(\n            filters: {\n                status: $status\n            }\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n":
    types.GetAllProductsDocument,
  "\n    query GetProductById($id: ID!) {\n        masterProductGetById(\n            id: $id\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            documents {\n                id\n                file {\n                    signedReadURL\n                    signedWriteURL\n                }\n            }\n            customTags {\n                id\n                key\n                label\n                value\n                type\n                description\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n":
    types.GetProductByIdDocument,
};

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = gql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function gql(source: string): unknown;

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation CompanyUserCreate($input: CompanyUserCreateInput!) {\n    companyUserCreate(input: $input) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n",
): (typeof documents)["\n  mutation CompanyUserCreate($input: CompanyUserCreateInput!) {\n    companyUserCreate(input: $input) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation CompanyUserInvite($companyUserId: ID!) {\n    companyUserInvite(companyUserId: $companyUserId) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n",
): (typeof documents)["\n  mutation CompanyUserInvite($companyUserId: ID!) {\n    companyUserInvite(companyUserId: $companyUserId) {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation CompanyUserDeactivate($companyUserId: ID!) {\n    companyUserDeactivate(companyUserId: $companyUserId) {\n      success\n      message\n    }\n  }\n",
): (typeof documents)["\n  mutation CompanyUserDeactivate($companyUserId: ID!) {\n    companyUserDeactivate(companyUserId: $companyUserId) {\n      success\n      message\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation CompanyUserActivate {\n    companyUserActivate {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n",
): (typeof documents)["\n  mutation CompanyUserActivate {\n    companyUserActivate {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation CreateCompanyBasicDetails($input: CompanyCreateBasicDetailsInput!) {\n    companyCreateBasicDetails(input: $input) {\n      id\n      basicDetails {\n        name\n        address\n        phoneNumber\n        email\n        website\n        industry\n        size\n      }\n    }\n  }\n",
): (typeof documents)["\n  mutation CreateCompanyBasicDetails($input: CompanyCreateBasicDetailsInput!) {\n    companyCreateBasicDetails(input: $input) {\n      id\n      basicDetails {\n        name\n        address\n        phoneNumber\n        email\n        website\n        industry\n        size\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\nmutation CustomerUpsertBasicDetails($input: CustomerUpsertBasicDetailsInput!) {\n    customerUpsertBasicDetails(input: $input) {\n        id\n    }\n  }\n",
): (typeof documents)["\nmutation CustomerUpsertBasicDetails($input: CustomerUpsertBasicDetailsInput!) {\n    customerUpsertBasicDetails(input: $input) {\n        id\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation CustomerUpsertAdditionalDetails($input: CustomerUpsertAdditionalDetailsInput!) {\n      customerUpsertAdditionalDetails(input: $input) {\n          id\n    }\n  }\n",
): (typeof documents)["\n  mutation CustomerUpsertAdditionalDetails($input: CustomerUpsertAdditionalDetailsInput!) {\n      customerUpsertAdditionalDetails(input: $input) {\n          id\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation MasterProductUpsertDetails($input: MasterProductUpsertInput!) {\n    masterProductUpsertDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n",
): (typeof documents)["\n  mutation MasterProductUpsertDetails($input: MasterProductUpsertInput!) {\n    masterProductUpsertDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  mutation MasterProductUpsertAdditionalDetails($input: MasterProductUpsertAdditionalDetailsInput!) {\n    masterProductUpsertAdditionalDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n",
): (typeof documents)["\n  mutation MasterProductUpsertAdditionalDetails($input: MasterProductUpsertAdditionalDetailsInput!) {\n    masterProductUpsertAdditionalDetails(input: $input) {\n      id\n      pricing {\n        id\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query Ok {\n    ok\n  }\n",
): (typeof documents)["\n  query Ok {\n    ok\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query GetCompany {\n    getCompany {\n      id\n      name\n      status\n      onboarding {\n        id\n        pendingSteps {\n          stepType\n          mandatory\n          completed\n        }\n        completedSteps {\n          stepType\n          mandatory\n          completed\n        }\n      }\n    }\n  }\n",
): (typeof documents)["\n  query GetCompany {\n    getCompany {\n      id\n      name\n      status\n      onboarding {\n        id\n        pendingSteps {\n          stepType\n          mandatory\n          completed\n        }\n        completedSteps {\n          stepType\n          mandatory\n          completed\n        }\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query GetCompanyBasicDetails {\n    getCompany {\n      id\n      basicDetails {\n        name\n        address\n        email\n        website\n        phoneNumber\n        industry\n        size\n      }\n    }\n  }\n",
): (typeof documents)["\n  query GetCompanyBasicDetails {\n    getCompany {\n      id\n      basicDetails {\n        name\n        address\n        email\n        website\n        phoneNumber\n        industry\n        size\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query GetCompanyUsers {\n    getCompany {\n      id\n      users {\n        id\n        name\n        email\n        status\n        roles\n      }\n    }\n  }\n",
): (typeof documents)["\n  query GetCompanyUsers {\n    getCompany {\n      id\n      users {\n        id\n        name\n        email\n        status\n        roles\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query GetCompanyUser {\n    getCompanyUser {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n",
): (typeof documents)["\n  query GetCompanyUser {\n    getCompanyUser {\n      id\n      name\n      email\n      status\n      roles\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query GetAllCustomers($status: CustomerStatus, $stage: CustomerStage, $customerType: CustomerType) {\n      getCustomers(\n        filters: {\n          status: $status,\n          stage: $stage,\n          customerType: $customerType\n        }\n      ) {\n        id\n        company {\n          id\n        }\n        status\n        stage\n        type\n        ...on CustomerIndividual {\n          basicDetails {\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n              referralSource\n        }\n      }\n      ... on CustomerBusiness {\n          basicDetails {\n              legalName\n              website\n              size\n              industry\n              referralSource\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n          }\n      }\n    }\n  }\n",
): (typeof documents)["\n  query GetAllCustomers($status: CustomerStatus, $stage: CustomerStage, $customerType: CustomerType) {\n      getCustomers(\n        filters: {\n          status: $status,\n          stage: $stage,\n          customerType: $customerType\n        }\n      ) {\n        id\n        company {\n          id\n        }\n        status\n        stage\n        type\n        ...on CustomerIndividual {\n          basicDetails {\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n              referralSource\n        }\n      }\n      ... on CustomerBusiness {\n          basicDetails {\n              legalName\n              website\n              size\n              industry\n              referralSource\n              contactDetails {\n                  contactType\n                  name\n                  title\n                  email\n                  phoneNo\n              }\n          }\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n  query GetCustomerById($id: ID!) {\n    getCustomer(id: $id) {\n      id\n      status\n      stage\n      type\n      company {\n        id\n      }\n      ... on CustomerIndividual {\n        basicDetails {\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n          referralSource\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n      ... on CustomerBusiness { \n        basicDetails {\n          legalName\n          website\n          size\n          industry\n          referralSource\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n   }\n}\n",
): (typeof documents)["\n  query GetCustomerById($id: ID!) {\n    getCustomer(id: $id) {\n      id\n      status\n      stage\n      type\n      company {\n        id\n      }\n      ... on CustomerIndividual {\n        basicDetails {\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n          referralSource\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n      ... on CustomerBusiness { \n        basicDetails {\n          legalName\n          website\n          size\n          industry\n          referralSource\n          contactDetails {\n            contactType\n            name\n            title\n            email\n            phoneNo\n          }\n        }\n        notes {\n          id\n          content\n          tags\n        }\n        assignments {\n          accountManager {\n            id\n            name\n            email\n          }\n          supportRepresentative {\n            id\n            name\n            email\n          }\n        }\n        customTags {\n          id\n          key\n          label\n          value\n          type\n          description\n        }\n      }\n   }\n}\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n    query GetAllProducts($status: ProductStatus) {\n        masterProductsGet(\n            filters: {\n                status: $status\n            }\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n",
): (typeof documents)["\n    query GetAllProducts($status: ProductStatus) {\n        masterProductsGet(\n            filters: {\n                status: $status\n            }\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: "\n    query GetProductById($id: ID!) {\n        masterProductGetById(\n            id: $id\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            documents {\n                id\n                file {\n                    signedReadURL\n                    signedWriteURL\n                }\n            }\n            customTags {\n                id\n                key\n                label\n                value\n                type\n                description\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n",
): (typeof documents)["\n    query GetProductById($id: ID!) {\n        masterProductGetById(\n            id: $id\n        ) {\n            id\n            name\n            description\n            status\n            productCode\n            dimensions {\n                key\n                value\n            }\n            documents {\n                id\n                file {\n                    signedReadURL\n                    signedWriteURL\n                }\n            }\n            customTags {\n                id\n                key\n                label\n                value\n                type\n                description\n            }\n            pricing {\n                id\n                chargePolicy\n                costPrice {\n                    value\n                    currency\n                }\n                listPrice {\n                    value\n                    currency\n                }\n                sellingPrice {\n                    value\n                    currency\n                }\n                unit {\n                    unit\n                    unitType\n                }\n                discount {\n                    id\n                    discountType\n                    discountValue {\n                        value {\n                            value\n                            currency\n                        }\n                        percentage\n                    }\n                }\n            }\n        }\n    }\n"];

export function gql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> =
  TDocumentNode extends DocumentNode<infer TType, any> ? TType : never;
