import React, { useState, useEffect } from 'react';
import ComponentDocumentUpload, { DocumentInput } from '@/component/common/ComponentDocumentUpload';
import { ComponentCustomTagsInput } from '@/component/common/ComponentCustomTagInput';
import { CustomTagInput } from '@/component/common/ComponentCustomTagInput';
import { SaveIcon } from 'lucide-react';
import { useMasterProductUpsertAdditionalDetailsMutation, useGetProductByIdQuery } from '@/lib/graphql/types/generated/hooks';
import {
    MasterProductUpsertAdditionalDetailsInput,
    CustomTagType
} from '@/lib/graphql/types/generated/graphql';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentNote from '@/component/common/ComponentNote';

// Extended DocumentInput to handle existing documents with URLs
interface ExtendedDocumentInput extends DocumentInput {
    signedReadURL?: string;
    signedWriteURL?: string;
}

interface ComponentProductAdditionalDetailsSectionProps {
    productId: string;
    onSaveSuccess?: () => void;
}

const ComponentProductAdditionalDetailsSection: React.FC<ComponentProductAdditionalDetailsSectionProps> = ({
    productId,
    onSaveSuccess
}) => {
    const [documents, setDocuments] = useState<ExtendedDocumentInput[]>([]);
    const [customTags, setCustomTags] = useState<CustomTagInput[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [saveNote, setSaveNote] = useState<React.ReactNode | null>(null);

    const [masterProductUpsertAdditionalDetails] = useMasterProductUpsertAdditionalDetailsMutation();

    // Fetch existing product data to populate documents and custom tags in edit mode
    const { data: productData, refetch: refetchProductData } = useGetProductByIdQuery({
        variables: { id: productId },
        skip: !productId,
    });

    // Load existing product data when available
    useEffect(() => {
        if (productData?.masterProductGetById) {
            const product = productData.masterProductGetById;

            // Map existing documents to component ExtendedDocumentInput format
            // Note: For existing documents, we don't have the File object, just the URLs
            const existingDocuments: ExtendedDocumentInput[] = product.documents?.map(doc => ({
                id: doc.id,
                file: null, // Existing documents don't have File objects
                // Store the URLs in a way the component can handle
                signedReadURL: doc.file?.signedReadURL || undefined,
                signedWriteURL: doc.file?.signedWriteURL || undefined
            })) || [];

            // Map existing custom tags to component CustomTagInput format
            const existingCustomTags: CustomTagInput[] = product.customTags?.map(tag => ({
                id : tag.id,
                label: tag.label,
                key: tag.key,
                value: tag.value,
                type: tag.type as string, // Convert enum to string for component
                description: tag.description || ''
            })) || [];

            setDocuments(existingDocuments);
            setCustomTags(existingCustomTags);
        }
    }, [productData]);

    const mapToMasterProductUpsertAdditionalDetailsInput = (): MasterProductUpsertAdditionalDetailsInput => {
        // Convert component DocumentInput to GraphQL DocumentInput
        const graphqlDocuments = documents.map(doc => ({
            id: doc.id,
            file: doc.file ? undefined : (doc.signedReadURL || doc.signedWriteURL) ? {
                signedReadURL: doc.signedReadURL,
                signedWriteURL: doc.signedWriteURL
            } : undefined
        }));

        // Convert component CustomTagInput to GraphQL CustomTagInput
        const graphqlCustomTags = customTags.map(tag => ({
            id: tag.id || null, // Existing tags have IDs, new ones don't
            key: tag.key,
            label: tag.label,
            value: tag.value,
            type: tag.type as CustomTagType, // Convert string to enum
            description: tag.description
        }));

        return {
            masterProductId: productId,
            documents: graphqlDocuments,
            customTags: graphqlCustomTags
        };
    };

    const handleSaveAdditionalDetails = async () => {
        setIsLoading(true);
        setSaveNote(null);

        try {
            await masterProductUpsertAdditionalDetails({
                variables: {
                    input: mapToMasterProductUpsertAdditionalDetailsInput()
                },
                onCompleted: async (data) => {
                    if (data.masterProductUpsertAdditionalDetails) {
                        setIsLoading(false);
                        setSaveNote(<ComponentNote>Additional details saved successfully!</ComponentNote>);

                        // Refetch product data to get updated information
                        await refetchProductData();

                        // Call success callback if provided
                        onSaveSuccess?.();
                    }
                },
                onError: (error) => {
                    setIsLoading(false);
                    setSaveNote(<ComponentNote isError={true}>{getUserFriendlyErrorMessage(error as ApolloError)}</ComponentNote>);
                }
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="mt-12">
            <div className="mb-4">
                <h2 className="text-2xl font-bold text-gray-900">Additional Details</h2>
                <p className="text-sm text-gray-600">Attach documents and add custom tags to enrich your product information.</p>
            </div>

            {saveNote && (
                <div className="mb-6">
                    {saveNote}
                </div>
            )}

            <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col space-y-8">
                <ComponentDocumentUpload
                    value={documents as DocumentInput[]}
                    onDocumentsChange={(docs) => setDocuments(docs as ExtendedDocumentInput[])}
                />
                <ComponentCustomTagsInput value={customTags} onTagsChange={setCustomTags} />
                <div className="flex justify-end col-span-2 mx-5">
                    <button
                        type="button"
                        onClick={handleSaveAdditionalDetails}
                        disabled={isLoading}
                        className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <div className='flex items-center gap-2'>
                            <SaveIcon className='h-4 w-4' />
                            {isLoading ? 'Saving...' : 'Save Additional Details'}
                        </div>
                    </button>
                </div>
            </div>
        </div >
    );
};

export default ComponentProductAdditionalDetailsSection;